# AugmentCode-Free PowerShell启动脚本
# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host ""
Write-Host "================================================" -ForegroundColor Cyan
Write-Host "    AugmentCode-Free 工具启动器" -ForegroundColor Yellow
Write-Host "================================================" -ForegroundColor Cyan
Write-Host ""

# 检查Python是否安装
try {
    $pythonVersion = py --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 检测到Python版本：$pythonVersion" -ForegroundColor Green
    } else {
        throw "Python未安装"
    }
} catch {
    Write-Host "❌ 错误：未找到Python！" -ForegroundColor Red
    Write-Host ""
    Write-Host "请先安装Python 3.7或更高版本：" -ForegroundColor Yellow
    Write-Host "https://www.python.org/downloads/" -ForegroundColor Blue
    Write-Host ""
    Read-Host "按回车键退出"
    exit 1
}

# 检查并安装依赖
Write-Host ""
Write-Host "🔧 检查依赖包..." -ForegroundColor Yellow
try {
    py -m pip install -r requirements.txt --quiet
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 依赖检查完成" -ForegroundColor Green
    } else {
        throw "依赖安装失败"
    }
} catch {
    Write-Host "❌ 依赖安装失败！请检查网络连接。" -ForegroundColor Red
    Read-Host "按回车键退出"
    exit 1
}

Write-Host ""
Write-Host "🚀 启动AugmentCode-Free..." -ForegroundColor Green
Write-Host ""

# 启动主程序
try {
    py main.py
} catch {
    Write-Host ""
    Write-Host "❌ 程序异常退出" -ForegroundColor Red
    Write-Host ""
    Read-Host "按回车键退出"
}
