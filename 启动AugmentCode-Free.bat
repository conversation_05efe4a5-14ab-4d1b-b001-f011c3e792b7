@echo off
title AugmentCode-Free Launcher

echo.
echo ================================================
echo    AugmentCode-Free Tool Launcher
echo ================================================
echo.

:: Check if Python is installed
py --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found!
    echo.
    echo Please install Python 3.7 or higher:
    echo https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

:: Show Python version
echo Python version detected:
py --version

:: Check and install dependencies
echo.
echo Checking dependencies...
py -m pip install -r requirements.txt --quiet
if errorlevel 1 (
    echo Dependency installation failed! Please check network connection.
    pause
    exit /b 1
)

echo Dependencies check completed
echo.
echo Starting AugmentCode-Free...
echo.

:: Start main program
py main.py

:: If program exits with error, show error message
if errorlevel 1 (
    echo.
    echo Program exited with error
    echo.
    pause
)
