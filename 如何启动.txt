🚀 AugmentCode-Free 启动方法
================================

📋 多种启动方式（推荐按优先级选择）：

1️⃣ 【推荐】双击批处理文件启动：
   📁 双击 "启动AugmentCode-Free.bat" 文件
   ✅ 自动检查Python环境和依赖
   ✅ 自动安装缺失的依赖包
   ✅ 启动图形界面

2️⃣ 双击PowerShell脚本启动：
   📁 右键点击 "启动AugmentCode-Free.ps1" → "使用PowerShell运行"
   ✅ 彩色输出，更美观的启动过程
   ⚠️  可能需要修改PowerShell执行策略

3️⃣ 直接双击Python文件：
   📁 双击 "main.py" 文件
   ⚠️  需要确保已安装所有依赖
   ⚠️  如果出错可能没有友好的错误提示

4️⃣ 命令行启动：
   💻 打开命令提示符或PowerShell
   💻 切换到项目目录
   💻 运行：py main.py

🔧 依赖要求：
- Python 3.7 或更高版本
- psutil >= 5.8.0
- click >= 8.0.0  
- colorama >= 0.4.4

❓ 常见问题：
Q: 双击.py文件没反应？
A: 请使用批处理文件启动，或检查Python是否正确安装

Q: 提示缺少模块？
A: 使用批处理文件启动会自动安装依赖

Q: GUI界面没有出现？
A: 检查防火墙或安全软件是否阻止了程序运行

📞 技术支持：
如遇问题请查看GitHub仓库或提交issue
